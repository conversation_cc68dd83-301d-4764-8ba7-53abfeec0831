#!/usr/bin/env python3
"""
Test script to verify imports work correctly
"""

try:
    print("🔄 Testing imports...")
    
    # Test basic imports
    import requests
    print("✅ requests imported")
    
    import jwt
    print("✅ jwt imported")
    
    from datetime import datetime, timezone
    print("✅ datetime imported")
    
    # Test our module imports
    import sys
    import os
    sys.path.append('src')
    
    from rtmp_ingress_module import RTMPIngressManager, IngressInfo
    print("✅ RTMPIngressManager imported")
    print("✅ IngressInfo imported")
    
    # Test class instantiation
    manager = RTMPIngressManager()
    print("✅ RTMPIngressManager instantiated")
    
    # Test IngressInfo
    test_data = {'ingress_id': 'test', 'url': 'test_url', 'stream_key': 'test_key'}
    info = IngressInfo(test_data)
    print("✅ IngressInfo instantiated")
    print(f"   - ingress_id: {info.ingress_id}")
    print(f"   - url: {info.url}")
    print(f"   - stream_key: {info.stream_key}")
    
    print("\n🎉 All imports and instantiations successful!")
    
except ImportError as e:
    print(f"❌ Import error: {e}")
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    print(f"❌ Traceback: {traceback.format_exc()}")
