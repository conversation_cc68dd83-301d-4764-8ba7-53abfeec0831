#!/usr/bin/env python3
"""
Test script to verify RTMP ingress creation fix
"""

import requests
import json
import sys

def test_rtmp_ingress_creation():
    """Test the RTMP ingress creation endpoint"""
    
    # Test data
    test_data = {
        "session_id": "test_session_123",
        "teacher_id": "test_teacher_456", 
        "teacher_name": "Test Teacher Name"
    }
    
    # Test localhost first
    print("🔄 Testing localhost endpoint...")
    try:
        response = requests.post(
            "http://localhost:8012/api/rtmp-ingress/create",
            json=test_data,
            timeout=30
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ Localhost test PASSED")
                return True
            else:
                print("❌ Localhost test FAILED - success=False")
                return False
        else:
            print("❌ Localhost test FAILED - non-200 status")
            return False
            
    except Exception as e:
        print(f"❌ Localhost test ERROR: {e}")
        return False

def test_server_endpoint():
    """Test the server endpoint"""
    
    # Test data
    test_data = {
        "session_id": "test_session_456",
        "teacher_id": "test_teacher_789", 
        "teacher_name": "Test Teacher Server"
    }
    
    print("\n🔄 Testing server endpoint...")
    try:
        response = requests.post(
            "https://sasthra.in/api/rtmp-ingress/create",
            json=test_data,
            timeout=30
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ Server test PASSED")
                return True
            else:
                print("❌ Server test FAILED - success=False")
                return False
        else:
            print("❌ Server test FAILED - non-200 status")
            return False
            
    except Exception as e:
        print(f"❌ Server test ERROR: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting RTMP ingress tests...")
    
    # Test localhost
    localhost_success = test_rtmp_ingress_creation()
    
    # Test server
    server_success = test_server_endpoint()
    
    print("\n📊 Test Results:")
    print(f"Localhost: {'✅ PASS' if localhost_success else '❌ FAIL'}")
    print(f"Server: {'✅ PASS' if server_success else '❌ FAIL'}")
    
    if localhost_success and server_success:
        print("\n🎉 All tests passed!")
        sys.exit(0)
    else:
        print("\n💥 Some tests failed!")
        sys.exit(1)
